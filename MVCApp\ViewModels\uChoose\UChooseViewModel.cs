﻿using MVCApp.Models.UChoose;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MVCApp.ViewModels.uChoose
{
    public class UChooseViewModel
    {
        public string UserName { get; set; }
        public List<PointsModel> CardPoints { get; set; }
        public int TotalPointsToDate { get; set; }
        public int TotalAvailablePoints { get; set; }
        public int TotalVestedPoints { get; set; }
        public List<LoanModel> Loans { get; set; }
    }

    public class PointsModel
    {
        public int PointsToDate { get; set; }
        public int AvailablePoints { get; set; }
        public int VestedPoints { get; set; }
        public string CardName { get; set; }
        public bool CardStatus { get; set; }
        public string AccountNumber { get; set; }
        public int CardType { get; set; }

        public CreditOrDebit CreditOrDebit { get; set; }
        public string CardDescription { get; set; }
    }

    

    public enum CreditOrDebit
    {
        Credit,
        Debit
    }

    public class LoanModel
    {
        public string AccountNumber { get; set; }
        public string Id { get; set; }
        public string Description { get; set; }
    }
}
