﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using SymXchangePoweronWSDL;
using SymXchangeAccountWSDL;
using System;
using System.Diagnostics;
using MVCApp.ViewModels;
using System.Collections.Generic;
using System.ServiceModel;
using System.Threading.Tasks;
using MVCApp.Helpers;
using MVCApp.ViewModels.ODP;

namespace MVCApp.Controllers
{
    public class ODPController : Controller
    {
        private readonly IConfiguration _config;
        private SymXchangePoweronHelper _symxPoweronHelper;
        private SymXchangeAccountHelper _symxAccountHelper;

        public ODPController(IConfiguration config)
        {
            _config = config;
            _symxPoweronHelper = new SymXchangePoweronHelper(_config);
            _symxAccountHelper = new SymXchangeAccountHelper(_config);
        }

        public async Task<IActionResult> Index()
        {
            String accountNumber = HttpContext.Request.Form["AccountNumber"];
            short userNumber = Convert.ToInt16(HttpContext.Request.Form["UserNumber"]);
            string appKey = HttpContext.Request.Form["AppKey"];

            if (accountNumber == null || appKey != _config["AppKey"])
            {
                return View("Error", new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
            }

            executePowerOnReturnArrayResponse response = await _symxPoweronHelper.RunODPPoweron(accountNumber);

            var model = new ODPViewModel();
            var owners = new List <string>();
            owners.Add(response.Response.Body.RGLine[0].LineValue);
            model.AcctOwners = owners;
            model.OdpAcctNumCount = int.Parse(response.Response.Body.RGLine[1].LineValue);
            var odpAccounts = new List<string>();
            odpAccounts.Add(response.Response.Body.RGLine[2].LineValue);
            model.OdpAccounts = odpAccounts;
            model.OdpFoundName = response.Response.Body.RGLine[3].LineValue;
            model.OdpShareId = response.Response.Body.RGLine[4].LineValue;
            model.AccountNumber = accountNumber;
            model.UserNumber = userNumber;
            var dqReasons = new List<string>();
            var reasonCount = response.Response.Body.RGLine.Length;
            reasonCount -= 5;
            for (int i = 0; i < reasonCount; i++)
            {
                dqReasons.Add(response.Response.Body.RGLine[i+5].LineValue);
            }
            model.DqReasons = dqReasons;
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> AddOdp()
        {
            String accountNumber = HttpContext.Request.Form["accountNumber"];
            String shareID = HttpContext.Request.Form["shareID"];
            short userNumber = Convert.ToInt16(HttpContext.Request.Form["userNumber"]);

            try
            {
                var response = await _symxAccountHelper.AddOdpToShare(accountNumber, shareID, userNumber);

                // check success
                var successStatus = response.UpdateResponse.UpdateStatus.isAllFieldsUpdateSuccess;
                // do something with successStatus

                var model = new AddOdpPageModel();

                model.AccountNumber = accountNumber;
                model.ShareID = shareID;
                model.Success = response.UpdateResponse.UpdateStatus.isAllFieldsUpdateSuccess;
                return View(model);
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
            }
        }
    }
}
