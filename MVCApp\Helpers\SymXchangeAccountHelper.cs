﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.UserSecrets;
using SymXchangeAccountWSDL;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.ServiceModel;
using System.Threading.Tasks;
using System.Xml;

namespace MVCApp.Helpers
{
    public class SymXchangeAccountHelper
    {
        private readonly IConfiguration _config;
        private AccountServiceClient _accountClient;
        private DeviceInformation _device;
        private string _adminPassword;
        private BasicHttpsBinding _basicHttpsBinding;
        private CredentialsChoice _credChoice;

        public SymXchangeAccountHelper(IConfiguration config)
        {
            _config = config;
            var deviceNumber = _config.GetValue<short>("DeviceNumber");
            var deviceType = _config.GetValue<string>("DeviceType");
            _adminPassword = _config.GetValue<string>("SymXAdminPassword");

            _device = new DeviceInformation()
            {
                DeviceNumber = deviceNumber,
                DeviceType = deviceType
            };

            _basicHttpsBinding = new BasicHttpsBinding()
            {
                MaxBufferSize = **********,
                MaxBufferPoolSize = 524288,
                MaxReceivedMessageSize = **********
            };

            _credChoice = new CredentialsChoice()
            {
                Item = new AdministrativeCredentials()
                {
                    Password = _adminPassword
                }
            };
        }

        public async Task<updateShareByIDResponse> AddOdpToShare(string accountNumber, string shareID, short userId)
        {
            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var request = new UpdateShareByIDRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = "UpdateODPRequest",
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                ShareId = shareID,
                ShareUpdateableFields = new ShareUpdateableFields()
                {
                    OverdraftTolerance = 700.00M,
                    OverdraftToleranceSpecified = true
                }
            };

            return await _accountClient.updateShareByIDAsync(request);
        }

        public async Task<createLoanNoteResponse> CreateLoanNote(string accountNumber, string loanID, short userId, List<string> noteTextLines)
        {
            // Validate note text fields
            if (noteTextLines == null || !noteTextLines.Any())
            {
                throw new ArgumentException("Note text cannot be empty", nameof(noteTextLines));
            }
            if (noteTextLines.Count > 50)
            {
                throw new ArgumentException("Cannot exceed 50 note lines", nameof(noteTextLines));
            }
            foreach (var line in noteTextLines)
            {
                if (string.IsNullOrEmpty(line) || line.Length > 40)
                {
                    throw new ArgumentException("Each note line must be non-empty and not exceed 40 characters", nameof(noteTextLines));
                }
            }

            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");
            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var loanNote = new LoanNoteCreatableFields();
            loanNote.Text = new LoanNoteUpdateableFieldsText[noteTextLines.Count];
            
            // Create an entry for each note line
            for (int i = 0; i < noteTextLines.Count; i++)
            {
                loanNote.Text[i] = new LoanNoteUpdateableFieldsText()
                {
                    Text = noteTextLines[i],
                    EntryId = (short)(i + 1), // EntryId starts at 1
                    EntryIdSpecified = true
                };
            }
            
            loanNote.Code = 50;
            loanNote.CodeSpecified = true;
            loanNote.UserId = userId;
            loanNote.UserIdSpecified = true;

            var request = new CreateLoanNoteRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = $"CreateLoanNoteRequest_{DateTime.Now:yyyyMMddHHmmss}",
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                LoanId = loanID,
                LoanNoteCreatableFields = loanNote
            };

            // TEST CODE - REMOVE
            // Before calling createLoanNoteAsync, add this debugging code:
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(CreateLoanNoteRequest));
            using (var stringWriter = new StringWriter())
            using (var xmlWriter = XmlWriter.Create(stringWriter, new XmlWriterSettings { Indent = true }))
            {
                serializer.Serialize(xmlWriter, request);
                var xmlOutput = stringWriter.ToString();
                Console.WriteLine("Generated XML:");
                Console.WriteLine(xmlOutput);
                // Or log it: _logger.LogInformation("Request XML: {xml}", xmlOutput);
            }
            // END TEST CODE

            return await _accountClient.createLoanNoteAsync(request);
        }

        public async Task<searchLoanPagedSelectFieldsResponse> GetLoansByAccountNumber(string accountNumber, short userId)
        {
            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var request = new LoanSearchPagedSelectFieldsRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = $"GetLoansRequest_{DateTime.Now:yyyyMMddHHmmss}",
                AccountNumber = accountNumber,
                BranchId = 0,
                BranchIdSpecified = false,
                PagingRequestContext = new PagingRequestContext()
                {
                    NumberOfRecordsToReturn = 50,
                    NumberOfRecordsToReturnSpecified = true,
                    NumberOfRecordsToSkip = 0,
                    NumberOfRecordsToSkipSpecified = true,
                    Token = ""
                },
                SelectableFields = new LoanSingleSelectableFields()
                {
                    IncludeAllLoanFields = false,
                    IncludeAllLoanFieldsSpecified = true,
                    LoanFields = new LoanFields()
                    {
                        Description = true,
                        DescriptionSpecified = true,
                        Id = true,
                        IdSpecified = true
                    }
                },
                Query = "CloseDate<={date:'1900-01-01'}"
            };

            return await _accountClient.searchLoanPagedSelectFieldsAsync(request);
        }
    }
}
