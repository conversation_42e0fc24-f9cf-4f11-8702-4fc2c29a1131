﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.VisualBasic;
using MVCApp.Controllers;
using MVCApp.Models.UChoose;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Authenticators;
using RestSharp.Authenticators.OAuth2;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Runtime.ConstrainedExecution;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace MVCApp.Helpers
{
    public interface IUChooseHelper
    {
        public Task<string> GetToken();
    }

    public class UChooseHelper : IUChooseHelper
    {
        private readonly ILogger<UChooseController> _logger;
        private readonly IConfiguration _configuration;

        public UChooseHelper(ILogger<UChooseController> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public UChooseHelper() { }

        public async Task<string> GetToken()
        {
            X509Certificate2 cert = new(@_configuration["uChooseApiSettings:CertPath"], _configuration["uChooseApiSettings:Certpassword"]);

            var options = new RestClientOptions(_configuration["uChooseApiSettings:ApiUrl"])
            {
                Timeout = TimeSpan.FromSeconds(6),
                ClientCertificates = new X509CertificateCollection { cert },
                Authenticator = new HttpBasicAuthenticator(_configuration["uChooseApiSettings:TokenUserName"], _configuration["uChooseApiSettings:TokenPassword"])
            };
            var client = new RestClient(options);

            var request = new RestRequest("/oauth2/v1/token", Method.Post);
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
            request.AddHeader("x-fapi-financial-id", _configuration["uChooseApiSettings:FapiId"]);
            request.AddQueryParameter("scope", "/cs/api");
            request.AddParameter("grant_type", "client_credentials");

            var tokenResponse = await client.ExecuteAsync(request);
            var apiReturn = JsonConvert.DeserializeObject<UChooseGetPointsResponseModel>(tokenResponse.Content);
            var accessToken = apiReturn.Access_Token;
            return accessToken;
        }

        public async Task<UChooseGetPointsResponseModel> GetPointsByCardNumber(int cardType, string cardNum)
        {
            X509Certificate2 cert = new(@_configuration["uChooseApiSettings:CertPath"], _configuration["uChooseApiSettings:Certpassword"]);

            var accessToken = await GetToken();
            var options = new RestClientOptions(_configuration["uChooseApiSettings:ApiUrl"])
            {
                Timeout = TimeSpan.FromSeconds(6),
                ClientCertificates = new X509CertificateCollection { cert },
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(accessToken, "Bearer")
            };

            var client = new RestClient(options);

            var request = new RestRequest("uChooseRewards/v1/points", Method.Post);
            request.AddHeader("x-fapi-financial-id", _configuration["uChooseApiSettings:FapiId"]);
            request.AddHeader("Content-Type", "application/json");

            var vendorName = "";
            var financialId = "";
            if (cardType <= 40)
            {
                financialId = _configuration["uChooseApiSettings:DebitId"];
                vendorName = _configuration["uChooseApiSettings:DebitVendorName"];
            }
            else
            {
                financialId = _configuration["uChooseApiSettings:CreditId"];
                vendorName = _configuration["uChooseApiSettings:CreditVendorName"];
            }

            var body = new
            {
                client = new
                {
                    applicationName = _configuration["uChooseApiSettings:ApplicationName"],
                    auditId = _configuration["uChooseApiSettings:AuditId"],
                    id = financialId,
                    vendorName
                },
                rewardsKeyInfo = new
                {
                    useMultiAuth = _configuration["uChooseApiSettings:UseMultiAuth"],
                    cardNumber = cardNum
                }
            };

            request.AddJsonBody(body);

            var pointsResponse = await client.ExecuteAsync(request);

            var apiReturn = JsonConvert.DeserializeObject<UChooseGetPointsResponseModel>(pointsResponse.Content);

            return apiReturn;
        }

        public async Task<UChooseGetPointsResponseModel> GetPointsByAccountNumber(int cardType, string AccountNum)
        {
            X509Certificate2 cert = new(@_configuration["uChooseApiSettings:CertPath"], _configuration["uChooseApiSettings:Certpassword"]);

            var options = new RestClientOptions(_configuration["uChooseApiSettings:ApiUrl"])
            {
                Timeout = TimeSpan.FromSeconds(6),
                ClientCertificates = new X509CertificateCollection { cert },
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(await GetToken(), "Bearer")
            };

            var client = new RestClient(options);

            var request = new RestRequest("uChooseRewards/v1/points", Method.Post);
            request.AddHeader("x-fapi-financial-id", _configuration["uChooseApiSettings:FapiId"]);
            request.AddHeader("Content-Type", "application/json");

            string vendorName;
            string financialId;

            if (cardType <= 40)
            {
                financialId = _configuration["uChooseApiSettings:DebitId"];
                vendorName = _configuration["uChooseApiSettings:DebitVendorName"];
            }
            else
            {
                financialId = _configuration["uChooseApiSettings:CreditId"];
                vendorName = _configuration["uChooseApiSettings:CreditVendorName"];
            }

            var body = new
            {
                client = new
                {
                    applicationName = _configuration["uChooseApiSettings:ApplicationName"],
                    auditId = _configuration["uChooseApiSettings:AuditId"],
                    id = financialId,
                    vendorName
                },
                rewardsKeyInfo = new
                {
                    useMultiAuth = false,
                    accountNumber = AccountNum
                }
            };

            request.AddJsonBody(body);

            var pointsResponse = await client.ExecuteAsync(request);
            var apiReturn = JsonConvert.DeserializeObject<UChooseGetPointsResponseModel>(pointsResponse.Content);

            return apiReturn;
        }

        public async Task<RestResponse> AddPoints(string accessToken, int cardType, string cardNum, int points, string description = null)
        {
            _logger.LogInformation("AddPoints called with card type: {cardtype}, Account Number: {accountnumber} and number of points: {points}", cardType, cardNum, points);

            X509Certificate2 cert = new(@_configuration["uChooseApiSettings:CertPath"], _configuration["uChooseApiSettings:Certpassword"]);

            var options = new RestClientOptions(_configuration["uChooseApiSettings:ApiUrl"])
            {
                Timeout = TimeSpan.FromSeconds(60),
                ClientCertificates = new X509CertificateCollection { cert },
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(accessToken, "Bearer")
            };

            var client = new RestClient(options);

            var request = new RestRequest("uChooseRewards/v1/addpoints", Method.Post);
            request.AddHeader("x-fapi-financial-id", _configuration["uChooseApiSettings:FapiId"]);
            request.AddHeader("Content-Type", "application/json");

            string financialId;
            string vendorName;

            if (cardType <= 40)
            {
                financialId = _configuration["uChooseApiSettings:DebitId"];
                vendorName = _configuration["uChooseApiSettings:DebitVendorName"];
            }
            else
            {
                financialId = _configuration["uChooseApiSettings:CreditId"];
                vendorName = _configuration["uChooseApiSettings:CreditVendorName"];
            }
            
            // Use the provided description or a default if none is provided
            string pointsDescription = !string.IsNullOrEmpty(description)
                ? description
                : $"Points added on {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

            var body = new
            {
                client = new
                {
                    applicationName = _configuration["uChooseApiSettings:ApplicationName"],
                    auditId = _configuration["uChooseApiSettings:AuditId"],
                    id = financialId,
                    vendorName
                },
                rewardsKeyInfo = new
                {
                    useMultiAuth = false,
                    cardNumber = cardNum
                },
                addPoints = new
                {
                    points = points,
                    description = pointsDescription,
                    vest = true
                }
            };

            request.AddJsonBody(body);

            var pointsResponse = await client.ExecuteAsync(request);

            _logger.LogDebug("API Response: Status={status}, Content={content}",
                pointsResponse.StatusCode, pointsResponse.Content);

            return pointsResponse;
        }

        public async Task<HttpStatusCode> RedeemPoints(int cardType, string cardNum, int points, string redeemOption = null, string rewardDescription = null)
        {
            _logger.LogInformation("RedeemPoints called with card type: {cardtype}, Card Number: {cardNum} and number of points: {points} redeem Option: {option}, Description: {description}",
                cardType, cardNum, points, redeemOption, rewardDescription);
            var token = await GetToken();

            X509Certificate2 cert = new(@_configuration["uChooseApiSettings:CertPath"], _configuration["uChooseApiSettings:Certpassword"]);

            var options = new RestClientOptions(_configuration["uChooseApiSettings:ApiUrl"])
            {
                Timeout = TimeSpan.FromSeconds(60),
                ClientCertificates = new X509CertificateCollection { cert },
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(token, "Bearer")
            };

            var client = new RestClient(options);

            var request = new RestRequest("uChooseRewards/v1/redeempoints", Method.Post);
            request.AddHeader("x-fapi-financial-id", _configuration["uChooseApiSettings:FapiId"]);
            request.AddHeader("Content-Type", "application/json");

            string financialId;
            string vendorName;

            if (cardType <= 40)
            {
                financialId = _configuration["uChooseApiSettings:DebitId"];
                vendorName = _configuration["uChooseApiSettings:DebitVendorName"];
            }
            else
            {
                financialId = _configuration["uChooseApiSettings:CreditId"];
                vendorName = _configuration["uChooseApiSettings:CreditVendorName"];
            }

            // Generate a unique reference ID for this redemption
            string referenceId = $"REDEEM_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid().ToString()[..8]}";

            // Use the provided reward description or a default if none is provided
            string description = !string.IsNullOrEmpty(rewardDescription)
                ? rewardDescription
                : $"Points redemption for {redeemOption ?? "reward"}";

            var body = new
            {
                client = new
                {
                    applicationName = _configuration["uChooseApiSettings:ApplicationName"],
                    auditId = _configuration["uChooseApiSettings:AuditId"],
                    id = financialId,
                    vendorName
                },
                rewardsKeyInfo = new
                {
                    useMultiAuth = false,
                    cardNumber = cardNum
                },
                redeemPoints = new
                {
                    points,
                    description,
                    reference = referenceId
                }
            };

            request.AddJsonBody(body);

            var pointsResponse = await client.ExecuteAsync(request);

            return pointsResponse.StatusCode == HttpStatusCode.OK ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
        }

        public async Task<UChooseGetPointsResponseModel> GetSSO(string accessToken, int cardType, string AccountNum)
        {
            X509Certificate2 cert = new(@_configuration["uChooseApiSettings:CertPath"], _configuration["uChooseApiSettings:Certpassword"]);

            var options = new RestClientOptions(_configuration["uChooseApiSettings:ApiUrl"])
            {
                Timeout = TimeSpan.FromSeconds(6),
                ClientCertificates = new X509CertificateCollection { cert },
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(accessToken, "Bearer")
            };

            var client = new RestClient(options);

            var request = new RestRequest("uChooseRewards/v1/sso-url", Method.Post);
            request.AddHeader("x-fapi-financial-id", _configuration["uChooseApiSettings:FapiId"]);
            request.AddHeader("Content-Type", "application/json");

            string financialId;
            string vendorName;

            if (cardType <= 40)
            {
                financialId = _configuration["uChooseApiSettings:DebitId"];
                vendorName = _configuration["uChooseApiSettings:DebitVendorName"];
            }
            else
            {
                financialId = _configuration["uChooseApiSettings:CreditId"];
                vendorName = _configuration["uChooseApiSettings:CreditVendorName"];
            }

            var body = new
            {
                client = new
                {
                    applicationName = _configuration["uChooseApiSettings:ApplicationName"],
                    auditId = _configuration["uChooseApiSettings:AuditId"],
                    id = financialId,
                    vendorName
                },
                rewardsKeyInfo = new
                {
                    useMultiAuth = false,
                    accountNumber = AccountNum
                },
                email = "<EMAIL>"
            };

            request.AddJsonBody(body);

            var pointsResponse = await client.ExecuteAsync(request);
            var apiReturn = JsonConvert.DeserializeObject<UChooseGetPointsResponseModel>(pointsResponse.Content);

            return apiReturn;
        }

        public async Task<HttpStatusCode> TransferPoints(int receivingCardType, string receivingCardNum, int burnCardType, string burnCardNum, int points)
        {
            _logger.LogInformation("Transfer Points called with receiving card type: {cardtype}, receiving Card Number: {receivingCardNum} and number of points to transfer: {points} from account type {accountType} Card: {burnCardNum}",
                receivingCardType, receivingCardNum, points, burnCardType, burnCardNum);

            try
            {
                var token = await GetToken();

                // Generate a unique transfer ID to link the transactions
                string transferId = $"TRANSFER_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid().ToString()[..8]}";

                // Create descriptions for the points transfer that reference each other
                string receivingLastFour = receivingCardNum.Length > 4 ? receivingCardNum[^4..] : receivingCardNum;
                string burnLastFour = burnCardNum.Length > 4 ? burnCardNum[^4..] : burnCardNum;
                string burnDescription = $"Points transfer to card ending in {receivingLastFour} (Ref: {transferId})";
                string addDescription = $"Points received from card ending in {burnLastFour} (Ref: {transferId})";

                // Step 1: Redeem (burn) points from the source card
                _logger.LogInformation("Redeeming {points} points from card {burnCardNum} with description: {description}",
                    points, burnCardNum, burnDescription);

                var burnPoints = await RedeemPoints(burnCardType, burnCardNum, points, "PointsTransfer", burnDescription);

                if (burnPoints == HttpStatusCode.OK)
                {
                    // Step 2: Add points to the receiving card
                    _logger.LogInformation("Adding {points} points to card {receivingCardNum} with description: {description}",
                        points, receivingCardNum, addDescription);

                    var addPointsResponse = await AddPoints(token, receivingCardType, receivingCardNum, points, addDescription);

                    if (addPointsResponse.IsSuccessful)
                    {
                        _logger.LogInformation("Successfully transferred {points} points from {burnCardNum} to {receivingCardNum}",
                            points, burnCardNum, receivingCardNum);
                        return HttpStatusCode.OK;
                    }
                    else
                    {
                        _logger.LogError("Failed to add points to receiving card. Status: {status}, Response: {content}",
                            addPointsResponse.StatusCode, addPointsResponse.Content);
                        return HttpStatusCode.BadRequest;
                    }
                }
                else
                {
                    _logger.LogError("Failed to redeem points from source card. Status: {status}", burnPoints);
                    return HttpStatusCode.BadRequest;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during points transfer from {burnCardNum} to {receivingCardNum}",
                    burnCardNum, receivingCardNum);
                return HttpStatusCode.InternalServerError;
            }
        }
    }
}



